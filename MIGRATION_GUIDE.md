# 从 Rsbuild 迁移到 Vue CLI 配置指南

## 迁移概述

本项目已成功从 Rsbuild 迁移到 Vue CLI 配置，使用 `vue.config.js` 替代了 `rsbuild.config.js`。

## 主要变更

### 1. 依赖变更

**移除的依赖：**
- `@rsbuild/core`
- `@rsbuild/plugin-babel`
- `@rsbuild/plugin-node-polyfill`
- `@rsbuild/plugin-sass`
- `@rsbuild/plugin-vue2`
- `@rsbuild/plugin-vue2-jsx`
- `rsbuild-svg-sprite-loader`

**新增的依赖：**
- `terser-webpack-plugin` - 用于生产环境代码压缩和移除 console

### 2. 脚本命令变更

```json
{
  "dev": "vue-cli-service serve",
  "build:prod": "vue-cli-service build",
  "build:stage": "vue-cli-service build --mode staging",
  "preview": "vue-cli-service serve --mode production"
}
```

### 3. 配置文件变更

- **删除：** `rsbuild.config.js`
- **更新：** `vue.config.js` - 包含了所有原 rsbuild 配置的功能

## 功能对照表

| Rsbuild 功能 | Vue CLI 等效配置 | 说明 |
|-------------|-----------------|------|
| `dev.lazyCompilation` | Vue CLI 默认支持 | 懒编译功能 |
| `source.define` | 环境变量自动注入 | VUE_APP_ 前缀变量 |
| `source.alias` | `configureWebpack.resolve.alias` | 路径别名 |
| `output.assetPrefix` | `publicPath` | 静态资源路径 |
| `server.proxy` | `devServer.proxy` | 开发服务器代理 |
| `performance.removeConsole` | `terser-webpack-plugin` | 移除 console |
| `performance.chunkSplit` | `optimization.splitChunks` | 代码分割 |
| `pluginSvgSpriteLoader` | `svg-sprite-loader` | SVG 图标处理 |

## 环境配置

项目支持三个环境：

1. **开发环境** (`.env.development`)
   - `VUE_APP_BASE_API = '/prod-api'`
   - `VUE_APP_YGF_BASE_URL = '/'`

2. **预发布环境** (`.env.staging`)
   - `VUE_APP_BASE_API = '/prod-api'`
   - `VUE_APP_YGF_BASE_URL = '/ygfZlb/'`

3. **生产环境** (`.env.production`)
   - `VUE_APP_BASE_API = '/prod-api'`
   - `VUE_APP_YGF_BASE_URL = '/ygfZlb/'`

## 使用方法

### 开发
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build:prod
```

### 构建预发布版本
```bash
npm run build:stage
```

### 预览生产版本
```bash
npm run preview
```

## 注意事项

1. **SVG 图标路径**：确保 SVG 图标文件位于 `src/assets/icons/svg` 目录下
2. **代理配置**：开发环境代理目标为 `http://************`
3. **代码分割**：保持了原有的分包策略，包括 ElementUI 单独分包
4. **压缩配置**：生产环境自动移除 console 并启用 gzip 压缩

## 兼容性

- 保持了所有原有功能
- 环境变量配置完全兼容
- 构建输出结构保持一致
- 开发服务器功能完全保留
